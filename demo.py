#!/usr/bin/env python3
"""
عرض توضيحي لمجيب الأسئلة الذكي
"""

import requests
import json
import time
import threading
from simple_app import app

def start_server():
    """تشغيل الخادم في خيط منفصل"""
    app.run(debug=False, host='localhost', port=5000, use_reloader=False)

def test_questions():
    """اختبار الأسئلة"""
    base_url = "http://localhost:5000"
    
    # انتظار تشغيل الخادم
    time.sleep(2)
    
    print("🤖 مرحباً بك في مجيب الأسئلة الذكي!")
    print("=" * 50)
    
    # أسئلة تجريبية
    test_questions = [
        {
            "question": "الشمس تشرق من الشرق - صح أم خطأ؟",
            "expected": "صح"
        },
        {
            "question": "الأرض مسطحة - صحيح أم خاطئ؟",
            "expected": "خطأ"
        },
        {
            "question": "ما هي عاصمة مصر؟\nأ) الإسكندرية\nب) القاهرة\nج) الجيزة\nد) أسوان",
            "expected": "ب"
        },
        {
            "question": "ما هي أكبر قارة في العالم؟\nأ) أفريقيا\nب) آسيا\nج) أوروبا\nد) أمريكا الشمالية",
            "expected": "ب"
        }
    ]
    
    for i, test in enumerate(test_questions, 1):
        print(f"\n📝 السؤال {i}:")
        print(f"❓ {test['question'][:50]}...")
        print(f"⏱️  جاري التحليل...")
        
        try:
            response = requests.post(
                f"{base_url}/answer",
                json={"question": test['question']},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('answer', 'غير محدد')
                confidence = result.get('confidence', 0)
                
                print(f"✅ الإجابة: {answer}")
                print(f"📊 مستوى الثقة: {confidence:.0%}")
                print(f"🎯 الإجابة المتوقعة: {test['expected']}")
                
                if answer == test['expected']:
                    print("🎉 إجابة صحيحة!")
                else:
                    print("⚠️  إجابة مختلفة عن المتوقع")
                    
            else:
                print(f"❌ خطأ في الخادم: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
        
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🎊 انتهى العرض التوضيحي!")
    print("🌐 افتح المتصفح واذهب إلى: http://localhost:5000")
    print("📱 جرب أسئلتك الخاصة!")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء العرض التوضيحي...")
    
    # تشغيل الخادم في خيط منفصل
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # تشغيل الاختبارات
    test_questions()
    
    # إبقاء الخادم يعمل
    print("\n⌨️  اضغط Ctrl+C للخروج")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 شكراً لاستخدام مجيب الأسئلة الذكي!")

if __name__ == "__main__":
    main()
