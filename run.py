#!/usr/bin/env python3
"""
ملف تشغيل التطبيق مع إعدادات محسنة
"""

import os
from app import app
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

if __name__ == '__main__':
    # إعدادات التشغيل
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"🚀 بدء تشغيل مجيب الأسئلة الذكي...")
    print(f"📍 العنوان: http://{host}:{port}")
    print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")
    print(f"🤖 الذكاء الاصطناعي: Hugging Face API")
    print("=" * 50)
    
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )
