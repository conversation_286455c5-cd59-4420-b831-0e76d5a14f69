from flask import Flask, render_template, request, jsonify
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/answer', methods=['POST'])
def answer_question():
    data = request.get_json()
    question = data.get('question', '')
    
    if not question:
        return jsonify({'error': 'لم يتم تقديم سؤال'})
    
    # تحليل بسيط للسؤال
    question_lower = question.lower()
    
    # تحديد نوع السؤال
    if any(indicator in question for indicator in ["صح أم خطأ", "صحيح أم خاطئ", "True or False"]):
        # سؤال صح/خطأ
        if any(word in question_lower for word in ['الشمس', 'شرق', 'صحيح', 'واضح']):
            answer = "صح"
        else:
            answer = "خطأ"
        
        result = {
            "type": "true_false",
            "answer": answer,
            "confidence": 0.8,
            "reasoning": "تحليل بسيط للسؤال"
        }
    elif any(indicator in question for indicator in ["أ)", "ب)", "ج)", "د)"]):
        # سؤال اختيار متعدد
        if "عاصمة مصر" in question:
            answer = "ب"  # القاهرة
        elif "أكبر قارة" in question:
            answer = "ب"  # آسيا
        else:
            answer = "أ"  # افتراضي
        
        result = {
            "type": "multiple_choice",
            "answer": answer,
            "confidence": 0.7,
            "reasoning": "تحليل بسيط للسؤال"
        }
    else:
        result = {
            "type": "unknown",
            "answer": "غير قادر على تحديد نوع السؤال",
            "confidence": 0.5,
            "reasoning": "نوع السؤال غير مدعوم"
        }
    
    # إضافة الوقت
    result['timestamp'] = datetime.now().isoformat()
    result['processing_time'] = '< 1 second'
    
    return jsonify(result)

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    print("🚀 بدء تشغيل مجيب الأسئلة الذكي...")
    print("📍 العنوان: http://localhost:5000")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5000)
