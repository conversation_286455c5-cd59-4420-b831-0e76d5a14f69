<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 نظام الإجابة التلقائية المبسط</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4ECDC4;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            margin: 5px;
        }
        
        .btn-primary {
            background: #4ECDC4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45b7aa;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #FF6B6B;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #4ECDC4;
        }
        
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #4ECDC4;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .log-panel {
            grid-column: 1 / -1;
            background: #263238;
            color: #e0e0e0;
            padding: 20px;
            border-radius: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #4ECDC4;
            display: none;
        }
        
        .api-info {
            grid-column: 1 / -1;
            background: #d1ecf1;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #bee5eb;
            margin-top: 20px;
        }
        
        .api-endpoint {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 5px 0;
            border-left: 3px solid #17a2b8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
            background: #f44336;
        }
        
        .status-running {
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 نظام الإجابة التلقائية المبسط</h1>
            <p>ربط سهل وسريع مع التطبيقات الخارجية</p>
        </div>
        
        <div class="main-content">
            <!-- لوحة التحكم -->
            <div class="panel">
                <h3>⚙️ لوحة التحكم</h3>
                
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="startSystem()">
                        🚀 بدء النظام
                    </button>
                    <button class="btn btn-danger" onclick="stopSystem()">
                        🛑 إيقاف النظام
                    </button>
                </div>
                
                <div style="text-align: center;">
                    <span id="systemStatus">النظام متوقف</span>
                    <span id="statusIndicator" class="status-indicator"></span>
                </div>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label for="testQuestion">اختبار سؤال:</label>
                    <textarea id="testQuestion" rows="3" placeholder="مثال: الشمس تشرق من الشرق - صح أم خطأ؟"></textarea>
                </div>
                
                <button class="btn btn-secondary" onclick="testQuestion()">
                    🔍 اختبار السؤال
                </button>
                
                <div id="testResult" class="test-result">
                    <!-- نتيجة الاختبار -->
                </div>
            </div>
            
            <!-- لوحة الإحصائيات -->
            <div class="panel">
                <h3>📊 الإحصائيات</h3>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="questionsAnswered">0</div>
                        <div class="stat-label">الأسئلة المجابة</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-value" id="accuracy">0%</div>
                        <div class="stat-label">الدقة المتوقعة</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-value" id="elapsedTime">00:00:00</div>
                        <div class="stat-label">الوقت المنقضي</div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-value" id="lastAnswer">-</div>
                        <div class="stat-label">آخر إجابة</div>
                    </div>
                </div>
                
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong>السؤال الحالي:</strong>
                    <div id="currentQuestion" style="margin-top: 5px; color: #666;">لا يوجد</div>
                </div>
            </div>
            
            <!-- معلومات API -->
            <div class="api-info">
                <h4>📡 معلومات API للمطورين</h4>
                <p>يمكن للتطبيقات الخارجية الاتصال بالنظام عبر:</p>
                
                <div class="api-endpoint">
                    POST /api/answer<br>
                    Body: {"question": "نص السؤال هنا"}
                </div>
                
                <div class="api-endpoint">
                    GET /api/stats - الإحصائيات
                </div>
                
                <div class="api-endpoint">
                    POST /api/test - اختبار سؤال
                </div>
                
                <p style="margin-top: 10px; font-size: 0.9em; color: #666;">
                    <strong>مثال JavaScript:</strong><br>
                    <code>fetch('/api/answer', {method: 'POST', headers: {'Content-Type': 'application/json'}, body: JSON.stringify({question: 'سؤالك هنا'})})</code>
                </p>
            </div>
            
            <!-- سجل الأنشطة -->
            <div class="log-panel">
                <h3 style="color: #4ECDC4; margin-bottom: 15px;">📝 سجل الأنشطة</h3>
                <div id="logContent">
                    جاري تحميل السجل...
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let updateInterval;
        
        function startSystem() {
            fetch('/api/start', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'started') {
                    isRunning = true;
                    updateSystemStatus(true);
                    startUpdates();
                    alert('تم بدء النظام بنجاح!');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في بدء النظام');
            });
        }
        
        function stopSystem() {
            fetch('/api/stop', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                isRunning = false;
                updateSystemStatus(false);
                stopUpdates();
                alert('تم إيقاف النظام');
            })
            .catch(error => {
                console.error('خطأ:', error);
            });
        }
        
        function testQuestion() {
            const question = document.getElementById('testQuestion').value;
            
            if (!question) {
                alert('يرجى إدخال سؤال للاختبار');
                return;
            }
            
            fetch('/api/test', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({question: question})
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                
                if (data.error) {
                    resultDiv.innerHTML = `<strong>خطأ:</strong> ${data.error}`;
                } else {
                    resultDiv.innerHTML = `
                        <strong>الإجابة:</strong> ${data.answer}<br>
                        <strong>مستوى الثقة:</strong> ${Math.round(data.confidence * 100)}%<br>
                        <strong>نوع السؤال:</strong> ${data.question_type}<br>
                        <strong>التفسير:</strong> ${data.reasoning}
                    `;
                }
                
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في الاختبار');
            });
        }
        
        function updateSystemStatus(running) {
            const statusText = document.getElementById('systemStatus');
            const statusIndicator = document.getElementById('statusIndicator');
            
            if (running) {
                statusText.textContent = 'النظام يعمل';
                statusIndicator.className = 'status-indicator status-running';
            } else {
                statusText.textContent = 'النظام متوقف';
                statusIndicator.className = 'status-indicator';
            }
        }
        
        function updateStats() {
            fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('questionsAnswered').textContent = data.questions_answered || 0;
                document.getElementById('accuracy').textContent = Math.round(data.accuracy || 0) + '%';
                document.getElementById('elapsedTime').textContent = data.elapsed_time || '00:00:00';
                document.getElementById('currentQuestion').textContent = data.current_question || 'لا يوجد';
                document.getElementById('lastAnswer').textContent = data.last_answer || '-';
            })
            .catch(error => {
                console.error('خطأ في تحديث الإحصائيات:', error);
            });
        }
        
        function updateLogs() {
            fetch('/api/logs')
            .then(response => response.json())
            .then(data => {
                const logContent = document.getElementById('logContent');
                logContent.innerHTML = data.logs.join('<br>');
                logContent.scrollTop = logContent.scrollHeight;
            })
            .catch(error => {
                console.error('خطأ في تحديث السجل:', error);
            });
        }
        
        function startUpdates() {
            updateInterval = setInterval(() => {
                updateStats();
                updateLogs();
            }, 2000);
        }
        
        function stopUpdates() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        }
        
        // تحديث أولي
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateLogs();
            
            // تحديث دوري للسجل
            setInterval(updateLogs, 3000);
        });
    </script>
</body>
</html>
