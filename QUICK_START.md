# 🚀 دليل التشغيل السريع

## تشغيل التطبيق

### الطريقة الأولى: التطبيق الكامل مع الذكاء الاصطناعي

1. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

2. **إعداد مفتاح Hugging Face (اختياري):**
   - اذهب إلى: https://huggingface.co/settings/tokens
   - أنشئ مفتاح مجاني
   - ضعه في ملف `.env`:
   ```
   HUGGINGFACE_API_TOKEN=hf_your_token_here
   ```

3. **تشغيل التطبيق:**
```bash
python app.py
```

### الطريقة الثانية: التطبيق المبسط (بدون ذكاء اصطناعي)

```bash
python simple_app.py
```

### الطريقة الثالثة: استخدام ملفات التشغيل

**على Windows:**
```bash
start.bat
```

**على Linux/Mac:**
```bash
./start.sh
```

## فتح التطبيق

بعد التشغيل، افتح المتصفح واذهب إلى:
```
http://localhost:5000
```

## اختبار التطبيق

```bash
python test_app.py
```

## أمثلة على الأسئلة

### أسئلة صح/خطأ:
- الشمس تشرق من الشرق - صح أم خطأ؟
- الأرض مسطحة - صحيح أم خاطئ؟

### أسئلة اختيار متعدد:
```
ما هي عاصمة مصر؟
أ) الإسكندرية
ب) القاهرة
ج) الجيزة
د) أسوان
```

## المميزات

✅ **يعمل بدون إنترنت** (النسخة المبسطة)
✅ **واجهة عربية جميلة**
✅ **عداد تنازلي 10 ثوانٍ**
✅ **دعم أسئلة صح/خطأ والاختيار المتعدد**
✅ **ذكاء اصطناعي مجاني** (النسخة الكاملة)

## استكشاف الأخطاء

### مشكلة في التشغيل:
```bash
# تأكد من تثبيت Flask
pip install Flask

# جرب التطبيق المبسط
python simple_app.py
```

### مشكلة في الذكاء الاصطناعي:
- استخدم التطبيق المبسط `simple_app.py`
- أو احصل على مفتاح Hugging Face مجاني

## النشر

### Heroku:
```bash
git init
git add .
git commit -m "Initial commit"
heroku create your-app-name
git push heroku main
```

### Railway:
- ارفع الملفات إلى GitHub
- اربط المشروع بـ Railway
- سيتم النشر تلقائياً

---

**ملاحظة:** التطبيق يعمل محلياً بدون إنترنت في النسخة المبسطة!
