@echo off
echo ========================================
echo    نظام الإجابة الذكي المتكامل
echo    Complete AI Question Answering System
echo ========================================
echo.

echo اختر النظام المطلوب:
echo 1. تطبيق الويب العادي (Flask Web App)
echo 2. نظام الإجابة التلقائية (Auto Answerer)
echo 3. العرض التوضيحي (Demo)
echo 4. الاختبار (Test)
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" goto webapp
if "%choice%"=="2" goto autoans
if "%choice%"=="3" goto demo
if "%choice%"=="4" goto test
goto invalid

:webapp
echo.
echo تشغيل تطبيق الويب...
echo افتح المتصفح واذهب إلى: http://localhost:5000
echo.
python simple_app.py
goto end

:autoans
echo.
echo تشغيل نظام الإجابة التلقائية...
echo.
python smart_auto_answerer.py
goto end

:demo
echo.
echo تشغيل العرض التوضيحي...
echo.
python demo.py
goto end

:test
echo.
echo تشغيل الاختبارات...
echo.
python test_app.py
goto end

:invalid
echo.
echo اختيار غير صحيح!
echo.

:end
pause
