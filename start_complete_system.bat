@echo off
echo ========================================
echo    نظام الإجابة الذكي المتكامل
echo    Complete AI Question Answering System
echo ========================================
echo.

echo اختر النظام المطلوب:
echo 1. تطبيق الويب العادي (Flask Web App)
echo 2. نظام الإجابة التلقائية عبر الويب (Web Auto Answerer) - جديد!
echo 3. مثال تطبيق خارجي (External App Example)
echo 4. العرض التوضيحي (Demo)
echo 5. الاختبار (Test)
echo.

set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" goto webapp
if "%choice%"=="2" goto webauto
if "%choice%"=="3" goto external
if "%choice%"=="4" goto demo
if "%choice%"=="5" goto test
goto invalid

:webapp
echo.
echo تشغيل تطبيق الويب...
echo افتح المتصفح واذهب إلى: http://localhost:5000
echo.
python simple_app.py
goto end

:webauto
echo.
echo تشغيل نظام الإجابة التلقائية عبر الويب...
echo واجهة التحكم: http://localhost:5001
echo.
python web_auto_answerer.py
goto end

:external
echo.
echo تشغيل مثال التطبيق الخارجي...
echo تأكد من تشغيل النظام التلقائي أولاً!
echo.
python external_app_example.py
goto end

:demo
echo.
echo تشغيل العرض التوضيحي...
echo.
python demo.py
goto end

:test
echo.
echo تشغيل الاختبارات...
echo.
python test_app.py
goto end

:invalid
echo.
echo اختيار غير صحيح!
echo.

:end
pause
