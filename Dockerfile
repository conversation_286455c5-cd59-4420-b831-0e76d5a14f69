# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات
COPY requirements.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مستخدم غير جذر
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# تعريف المنفذ
EXPOSE 5000

# متغيرات البيئة
ENV FLASK_ENV=production
ENV PYTHONPATH=/app

# أمر التشغيل
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "30", "app:app"]
