"""
إعدادات التطبيق
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """إعدادات أساسية"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # إعدادات Hugging Face
    HUGGINGFACE_API_TOKEN = os.environ.get('HUGGINGFACE_API_TOKEN')
    HUGGINGFACE_MODEL = os.environ.get('HUGGINGFACE_MODEL') or 'microsoft/DialoGPT-medium'
    
    # إعدادات التطبيق
    APP_NAME = os.environ.get('APP_NAME') or 'AI Question Answerer'
    APP_VERSION = os.environ.get('APP_VERSION') or '1.0.0'
    
    # إعدادات الوقت
    QUESTION_TIMEOUT = int(os.environ.get('QUESTION_TIMEOUT', 10))  # ثوانٍ
    API_TIMEOUT = int(os.environ.get('API_TIMEOUT', 5))  # ثوانٍ
    
    # إعدادات الأداء
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """إعدادات الاختبار"""
    DEBUG = True
    TESTING = True

# اختيار الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
