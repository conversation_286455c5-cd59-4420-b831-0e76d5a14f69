# 🤖 مجيب الأسئلة الذكي - AI Question Answerer

تطبيق Flask ذكي يجيب على الأسئلة تلقائياً باستخدام الذكاء الاصطناعي المجاني من Hugging Face.

## ✨ المميزات

- 🎯 **إجابة تلقائية** على أسئلة صح/خطأ والاختيار المتعدد
- 🧠 **ذكاء اصطناعي مجاني** باستخدام Hugging Face API
- ⏱️ **عداد تنازلي** 10 ثوانٍ لكل سؤال
- 🎨 **واجهة عربية جميلة** ومتجاوبة
- 📊 **عرض مستوى الثقة** في الإجابة
- 💡 **تفسير الإجابات** من الذكاء الاصطناعي

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. إ<PERSON>د<PERSON> مفتاح Hugging Face (مجاني)

1. اذه<PERSON> إلى: https://huggingface.co/settings/tokens
2. أنشئ حساب مجاني إذا لم يكن لديك
3. أنشئ مفتاح API جديد
4. انسخ المفتاح وضعه في ملف `.env`:

```
HUGGINGFACE_API_TOKEN=hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 3. تشغيل التطبيق

```bash
python app.py
```

أو للإنتاج:

```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 4. فتح التطبيق

افتح المتصفح واذهب إلى: http://localhost:5000

## 📝 كيفية الاستخدام

### أسئلة صح/خطأ
```
الشمس تشرق من الشرق - صح أم خطأ؟
```

### أسئلة الاختيار المتعدد
```
ما هي عاصمة مصر؟
أ) الإسكندرية
ب) القاهرة
ج) الجيزة
د) أسوان
```

## 🔧 التخصيص

### تغيير نموذج الذكاء الاصطناعي

في ملف `app.py`، يمكنك تغيير النموذج:

```python
self.api_url = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium"
```

### نماذج مجانية مقترحة:
- `microsoft/DialoGPT-medium` - للمحادثة
- `google/flan-t5-base` - للأسئلة والأجوبة
- `facebook/bart-large-mnli` - للتصنيف

### تغيير وقت العداد التنازلي

في ملف `templates/index.html`:

```javascript
let timeLeft = 10; // غير هذا الرقم
```

## 🌐 النشر

### Heroku
```bash
# إنشاء ملف Procfile
echo "web: gunicorn app:app" > Procfile

# رفع للـ Heroku
git init
git add .
git commit -m "Initial commit"
heroku create your-app-name
git push heroku main
```

### Railway
```bash
# إنشاء ملف railway.toml
echo "[build]
command = \"pip install -r requirements.txt\"
[deploy]
command = \"gunicorn app:app\"" > railway.toml
```

## 🔍 استكشاف الأخطاء

### مشكلة في API
- تأكد من صحة مفتاح Hugging Face
- تحقق من الاتصال بالإنترنت
- النموذج قد يكون مشغول، جرب نموذج آخر

### مشكلة في الإجابات
- التطبيق يستخدم تحليل بسيط كبديل إذا فشل AI
- يمكن تحسين دقة الإجابات بتدريب نموذج مخصص

## 📱 التطوير المستقبلي

- [ ] دعم المزيد من أنواع الأسئلة
- [ ] حفظ تاريخ الأسئلة والإجابات
- [ ] إضافة نظام نقاط
- [ ] دعم الصور في الأسئلة
- [ ] API للتطبيقات الخارجية

## 🤝 المساهمة

مرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. عمل Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر تحت رخصة MIT.

## 📞 الدعم

إذا واجهت أي مشاكل، يرجى إنشاء Issue في GitHub أو التواصل معي.

---

**ملاحظة**: هذا التطبيق يستخدم نماذج ذكاء اصطناعي مجانية، لذا قد تختلف دقة الإجابات. للحصول على دقة أعلى، يمكن استخدام نماذج مدفوعة مثل OpenAI GPT.
