<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجيب الأسئلة الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .question-input {
            margin-bottom: 30px;
        }
        
        .question-input label {
            display: block;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .question-input textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.1em;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        .question-input textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f44336;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #da190b;
        }
        
        .timer {
            background: #ff9800;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            display: none;
        }
        
        .result-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
            display: none;
        }
        
        .result-header {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .answer-display {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #4CAF50;
            margin-bottom: 15px;
        }
        
        .answer-text {
            font-size: 1.3em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .confidence {
            font-size: 1em;
            color: #666;
        }
        
        .reasoning {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .reasoning h4 {
            margin-bottom: 10px;
            color: #1976d2;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .examples {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .examples h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .example-item {
            background: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .example-item:hover {
            background: #f8f9fa;
        }
        
        @media (max-width: 600px) {
            .controls {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 مجيب الأسئلة الذكي</h1>
            <p>يجيب على أسئلة صح/خطأ والاختيار المتعدد باستخدام الذكاء الاصطناعي</p>
        </div>
        
        <div class="main-content">
            <div class="question-input">
                <label for="questionText">أدخل السؤال هنا:</label>
                <textarea id="questionText" placeholder="مثال: الأرض كروية الشكل - صح أم خطأ؟

أو

ما هي عاصمة مصر؟
أ) الإسكندرية
ب) القاهرة  
ج) الجيزة
د) أسوان"></textarea>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="answerQuestion()">🎯 الحصول على الإجابة</button>
                <button class="btn btn-secondary" onclick="clearAll()">🗑️ مسح الكل</button>
                <div class="timer" id="timer">⏱️ الوقت المتبقي: <span id="timeLeft">10</span> ثانية</div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري تحليل السؤال والبحث عن الإجابة...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <div class="result-header">📊 نتيجة التحليل</div>
                <div class="answer-display">
                    <div class="answer-text" id="answerText"></div>
                    <div class="confidence" id="confidenceText"></div>
                </div>
                <div class="reasoning" id="reasoning" style="display: none;">
                    <h4>🧠 تفسير الإجابة:</h4>
                    <p id="reasoningText"></p>
                </div>
            </div>
            
            <div class="examples">
                <h3>💡 أمثلة على الأسئلة المدعومة:</h3>
                <div class="example-item" onclick="setExample(this)">
                    الشمس تشرق من الشرق - صح أم خطأ؟
                </div>
                <div class="example-item" onclick="setExample(this)">
                    ما هي أكبر قارة في العالم؟
أ) أفريقيا
ب) آسيا
ج) أوروبا
د) أمريكا الشمالية
                </div>
                <div class="example-item" onclick="setExample(this)">
                    الماء يغلي عند درجة 100 مئوية - صحيح أم خاطئ؟
                </div>
            </div>
        </div>
    </div>

    <script>
        let timerInterval;
        
        function answerQuestion() {
            const questionText = document.getElementById('questionText').value.trim();
            
            if (!questionText) {
                alert('يرجى إدخال سؤال أولاً');
                return;
            }
            
            // إظهار التحميل
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            
            // بدء العداد التنازلي
            startTimer();
            
            // إرسال السؤال للخادم
            fetch('/answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: questionText
                })
            })
            .then(response => response.json())
            .then(data => {
                displayResult(data);
                stopTimer();
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في الاتصال بالخادم');
                stopTimer();
            });
        }
        
        function displayResult(data) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('resultSection').style.display = 'block';
            
            const answerText = document.getElementById('answerText');
            const confidenceText = document.getElementById('confidenceText');
            const reasoningDiv = document.getElementById('reasoning');
            const reasoningText = document.getElementById('reasoningText');
            
            // عرض الإجابة
            answerText.textContent = `الإجابة: ${data.answer}`;
            
            // عرض مستوى الثقة
            const confidence = Math.round((data.confidence || 0.5) * 100);
            confidenceText.textContent = `مستوى الثقة: ${confidence}%`;
            
            // عرض التفسير إذا كان متوفراً
            if (data.reasoning && data.reasoning !== data.answer) {
                reasoningText.textContent = data.reasoning;
                reasoningDiv.style.display = 'block';
            } else {
                reasoningDiv.style.display = 'none';
            }
            
            // تلوين الإجابة حسب النوع
            if (data.type === 'true_false') {
                answerText.style.color = data.answer === 'صح' ? '#4CAF50' : '#f44336';
            } else {
                answerText.style.color = '#4CAF50';
            }
        }
        
        function startTimer() {
            let timeLeft = 10;
            const timerDiv = document.getElementById('timer');
            const timeLeftSpan = document.getElementById('timeLeft');
            
            timerDiv.style.display = 'inline-block';
            timeLeftSpan.textContent = timeLeft;
            
            timerInterval = setInterval(() => {
                timeLeft--;
                timeLeftSpan.textContent = timeLeft;
                
                if (timeLeft <= 0) {
                    stopTimer();
                }
            }, 1000);
        }
        
        function stopTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
            document.getElementById('timer').style.display = 'none';
        }
        
        function clearAll() {
            document.getElementById('questionText').value = '';
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
            stopTimer();
        }
        
        function setExample(element) {
            document.getElementById('questionText').value = element.textContent.trim();
        }
        
        // السماح بالإرسال عند الضغط على Ctrl+Enter
        document.getElementById('questionText').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                answerQuestion();
            }
        });
    </script>
</body>
</html>
