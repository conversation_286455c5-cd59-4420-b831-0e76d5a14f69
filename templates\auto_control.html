<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 نظام الإجابة التلقائية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .control-panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .control-panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4ECDC4;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            margin: 5px;
        }
        
        .btn-primary {
            background: #4ECDC4;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45b7aa;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #FF6B6B;
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff5252;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .stats-panel {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #bbdefb;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .stat-label {
            font-weight: bold;
            color: #1976d2;
        }
        
        .stat-value {
            color: #424242;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-running {
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background: #f44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .log-panel {
            grid-column: 1 / -1;
            background: #263238;
            color: #e0e0e0;
            padding: 20px;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .log-panel h3 {
            color: #4ECDC4;
            margin-bottom: 15px;
        }
        
        .test-panel {
            grid-column: 1 / -1;
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #ffeaa7;
            margin-top: 20px;
        }
        
        .test-panel h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .test-result {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #4ECDC4;
        }
        
        .api-info {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #bee5eb;
            margin-top: 20px;
        }
        
        .api-info h4 {
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .api-endpoint {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 5px 0;
            border-left: 3px solid #17a2b8;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 نظام الإجابة التلقائية</h1>
            <p>ربط ذكي مع التطبيقات الخارجية للإجابة على الأسئلة تلقائياً</p>
        </div>
        
        <div class="main-content">
            <!-- لوحة التحكم -->
            <div class="control-panel">
                <h3>⚙️ لوحة التحكم</h3>
                
                <div class="form-group">
                    <label for="targetUrl">رابط التطبيق المستهدف:</label>
                    <input type="url" id="targetUrl" placeholder="https://kahoot.it/ أو أي رابط آخر">
                </div>
                
                <div class="form-group">
                    <label for="appType">نوع التطبيق:</label>
                    <select id="appType">
                        <option value="kahoot">Kahoot</option>
                        <option value="quizizz">Quizizz</option>
                        <option value="google_forms">Google Forms</option>
                        <option value="microsoft_forms">Microsoft Forms</option>
                        <option value="custom">تطبيق مخصص</option>
                    </select>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="startMonitoring()">
                        🚀 بدء المراقبة
                    </button>
                    <button class="btn btn-danger" onclick="stopMonitoring()">
                        🛑 إيقاف المراقبة
                    </button>
                </div>
                
                <div style="margin-top: 20px; text-align: center;">
                    <span id="systemStatus">النظام متوقف</span>
                    <span id="statusIndicator" class="status-indicator status-stopped"></span>
                </div>
            </div>
            
            <!-- لوحة الإحصائيات -->
            <div class="stats-panel">
                <h3>📊 الإحصائيات</h3>
                
                <div class="stat-item">
                    <span class="stat-label">الأسئلة المجابة:</span>
                    <span class="stat-value" id="questionsAnswered">0</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">الدقة المتوقعة:</span>
                    <span class="stat-value" id="accuracy">0%</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">الوقت المنقضي:</span>
                    <span class="stat-value" id="elapsedTime">00:00:00</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">السؤال الحالي:</span>
                    <span class="stat-value" id="currentQuestion">لا يوجد</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">آخر إجابة:</span>
                    <span class="stat-value" id="lastAnswer">لا يوجد</span>
                </div>
            </div>
            
            <!-- لوحة الاختبار -->
            <div class="test-panel">
                <h3>🧪 اختبار السؤال</h3>
                
                <div class="form-group">
                    <label for="testQuestion">أدخل سؤال للاختبار:</label>
                    <input type="text" id="testQuestion" placeholder="مثال: الشمس تشرق من الشرق - صح أم خطأ؟">
                </div>
                
                <button class="btn btn-secondary" onclick="testQuestion()">
                    🔍 اختبار السؤال
                </button>
                
                <div id="testResult" class="test-result" style="display: none;">
                    <!-- نتيجة الاختبار ستظهر هنا -->
                </div>
            </div>
            
            <!-- معلومات API -->
            <div class="api-info">
                <h4>📡 معلومات API للمطورين</h4>
                <p>يمكن للتطبيقات الخارجية الاتصال بالنظام عبر:</p>
                
                <div class="api-endpoint">
                    POST /api/answer<br>
                    Body: {"question": "نص السؤال هنا"}
                </div>
                
                <div class="api-endpoint">
                    GET /api/stats - للحصول على الإحصائيات
                </div>
                
                <div class="api-endpoint">
                    GET /api/logs - للحصول على السجل
                </div>
            </div>
            
            <!-- سجل الأنشطة -->
            <div class="log-panel">
                <h3>📝 سجل الأنشطة</h3>
                <div id="logContent">
                    جاري تحميل السجل...
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let updateInterval;
        
        function startMonitoring() {
            const url = document.getElementById('targetUrl').value;
            const appType = document.getElementById('appType').value;
            
            if (!url) {
                alert('يرجى إدخال رابط التطبيق');
                return;
            }
            
            fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: url,
                    app_type: appType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'started') {
                    isRunning = true;
                    updateSystemStatus(true);
                    startUpdates();
                    alert('تم بدء المراقبة بنجاح!');
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في الاتصال');
            });
        }
        
        function stopMonitoring() {
            fetch('/api/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                isRunning = false;
                updateSystemStatus(false);
                stopUpdates();
                alert('تم إيقاف المراقبة');
            })
            .catch(error => {
                console.error('خطأ:', error);
            });
        }
        
        function testQuestion() {
            const question = document.getElementById('testQuestion').value;
            
            if (!question) {
                alert('يرجى إدخال سؤال للاختبار');
                return;
            }
            
            fetch('/api/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                
                if (data.error) {
                    resultDiv.innerHTML = `<strong>خطأ:</strong> ${data.error}`;
                } else {
                    resultDiv.innerHTML = `
                        <strong>الإجابة:</strong> ${data.answer}<br>
                        <strong>مستوى الثقة:</strong> ${Math.round(data.confidence * 100)}%<br>
                        <strong>نوع السؤال:</strong> ${data.question_type}<br>
                        <strong>التفسير:</strong> ${data.reasoning || 'غير متوفر'}
                    `;
                }
                
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ في الاختبار');
            });
        }
        
        function updateSystemStatus(running) {
            const statusText = document.getElementById('systemStatus');
            const statusIndicator = document.getElementById('statusIndicator');
            
            if (running) {
                statusText.textContent = 'النظام يعمل';
                statusIndicator.className = 'status-indicator status-running';
            } else {
                statusText.textContent = 'النظام متوقف';
                statusIndicator.className = 'status-indicator status-stopped';
            }
        }
        
        function updateStats() {
            fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('questionsAnswered').textContent = data.questions_answered || 0;
                document.getElementById('accuracy').textContent = Math.round(data.accuracy || 0) + '%';
                document.getElementById('elapsedTime').textContent = data.elapsed_time || '00:00:00';
                document.getElementById('currentQuestion').textContent = data.current_question || 'لا يوجد';
                document.getElementById('lastAnswer').textContent = data.last_answer || 'لا يوجد';
            })
            .catch(error => {
                console.error('خطأ في تحديث الإحصائيات:', error);
            });
        }
        
        function updateLogs() {
            fetch('/api/logs')
            .then(response => response.json())
            .then(data => {
                const logContent = document.getElementById('logContent');
                logContent.innerHTML = data.logs.join('<br>');
                logContent.scrollTop = logContent.scrollHeight;
            })
            .catch(error => {
                console.error('خطأ في تحديث السجل:', error);
            });
        }
        
        function startUpdates() {
            updateInterval = setInterval(() => {
                updateStats();
                updateLogs();
            }, 2000); // تحديث كل ثانيتين
        }
        
        function stopUpdates() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        }
        
        // تحديث أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateLogs();
            
            // تحديث دوري للسجل حتى لو لم يكن النظام يعمل
            setInterval(updateLogs, 5000);
        });
    </script>
</body>
</html>
