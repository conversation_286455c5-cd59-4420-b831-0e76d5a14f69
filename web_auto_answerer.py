#!/usr/bin/env python3
"""
نظام الإجابة التلقائية عبر الويب - بدون Selenium
Web-based Auto Answerer without Selenium
"""

import requests
import time
import json
import re
from datetime import datetime
from app import AIQuestionAnswerer
import threading
from flask import Flask, render_template, request, jsonify, redirect, url_for

# إنشاء تطبيق Flask للتحكم
control_app = Flask(__name__, template_folder='templates')

class WebAutoAnswerer:
    """نظام الإجابة التلقائية عبر الويب"""
    
    def __init__(self):
        self.ai_answerer = AIQuestionAnswerer()
        self.is_running = False
        self.stats = {
            'questions_answered': 0,
            'correct_answers': 0,
            'start_time': None,
            'current_question': None,
            'last_answer': None
        }
        self.log_messages = []
        self.target_url = None
        self.session = requests.Session()
        
    def log_message(self, message):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)
        print(log_entry)
        
        # الاحتفاظ بآخر 100 رسالة فقط
        if len(self.log_messages) > 100:
            self.log_messages = self.log_messages[-100:]
    
    def extract_question_from_text(self, text):
        """استخراج السؤال من النص"""
        try:
            # تنظيف النص
            text = re.sub(r'\s+', ' ', text).strip()
            
            # البحث عن أنماط الأسئلة
            true_false_patterns = [
                r'(.+?)\s*[-–—]\s*(صح أم خطأ|صحيح أم خاطئ|True or False)',
                r'(.+?)\s*(صح أم خطأ|صحيح أم خاطئ|True or False)',
            ]
            
            multiple_choice_patterns = [
                r'(.+?)\s*[أا]\)\s*(.+?)\s*[بب]\)\s*(.+?)(?:\s*[جج]\)\s*(.+?))?(?:\s*[دد]\)\s*(.+?))?',
                r'(.+?)\s*A\)\s*(.+?)\s*B\)\s*(.+?)(?:\s*C\)\s*(.+?))?(?:\s*D\)\s*(.+?))?',
            ]
            
            # فحص أسئلة صح/خطأ
            for pattern in true_false_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return {
                        'text': match.group(1).strip(),
                        'type': 'true_false',
                        'options': []
                    }
            
            # فحص أسئلة الاختيار المتعدد
            for pattern in multiple_choice_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    question_text = match.group(1).strip()
                    options = []
                    for i in range(2, len(match.groups()) + 1):
                        if match.group(i):
                            options.append(match.group(i).strip())
                    
                    if len(options) >= 2:
                        return {
                            'text': question_text,
                            'type': 'multiple_choice',
                            'options': options
                        }
            
            # إذا لم يتم العثور على نمط محدد، اعتبره سؤال عام
            return {
                'text': text,
                'type': 'general',
                'options': []
            }
            
        except Exception as e:
            self.log_message(f"❌ خطأ في استخراج السؤال: {e}")
            return None
    
    def process_question(self, question_text):
        """معالجة السؤال والحصول على الإجابة"""
        try:
            # استخراج بيانات السؤال
            question_data = self.extract_question_from_text(question_text)
            
            if not question_data:
                return None
            
            self.stats['questions_answered'] += 1
            self.stats['current_question'] = question_data['text']
            
            # تكوين السؤال الكامل للذكاء الاصطناعي
            full_question = question_data['text']
            if question_data['options']:
                options_text = '\n'.join([f"{chr(65+i)}) {opt}" for i, opt in enumerate(question_data['options'])])
                full_question += '\n' + options_text
            
            self.log_message(f"📝 السؤال {self.stats['questions_answered']}:")
            self.log_message(f"❓ {question_data['text'][:100]}...")
            
            # الحصول على الإجابة من الذكاء الاصطناعي
            ai_result = self.ai_answerer.analyze_question(full_question)
            answer = ai_result.get('answer', 'أ')
            confidence = ai_result.get('confidence', 0.5)
            reasoning = ai_result.get('reasoning', '')
            
            self.stats['last_answer'] = answer
            
            self.log_message(f"🤖 الإجابة: {answer} (ثقة: {confidence:.0%})")
            
            if confidence > 0.7:
                self.stats['correct_answers'] += 1
            
            return {
                'question': question_data,
                'answer': answer,
                'confidence': confidence,
                'reasoning': reasoning,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_message(f"❌ خطأ في معالجة السؤال: {e}")
            return None
    
    def start_monitoring(self, target_url):
        """بدء مراقبة الرابط المحدد"""
        self.target_url = target_url
        self.is_running = True
        self.stats['start_time'] = datetime.now()
        
        self.log_message(f"🚀 بدء مراقبة: {target_url}")
        
        # في الواقع، هذا النظام يعمل كـ API للتطبيقات الأخرى
        # التطبيق الخارجي يرسل الأسئلة ويحصل على الإجابات
        self.log_message("✅ النظام جاهز لاستقبال الأسئلة")
        self.log_message("📡 يمكن للتطبيقات الخارجية إرسال الأسئلة إلى:")
        self.log_message("   POST /api/answer")
        self.log_message("   Body: {'question': 'نص السؤال هنا'}")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.is_running = False
        self.log_message("🛑 تم إيقاف المراقبة")

# إنشاء مثيل من النظام
auto_answerer = WebAutoAnswerer()

@control_app.route('/')
def index():
    """الصفحة الرئيسية للتحكم"""
    return render_template('auto_control.html')

@control_app.route('/api/start', methods=['POST'])
def start_monitoring():
    """بدء المراقبة"""
    data = request.get_json()
    target_url = data.get('url', '')
    
    if target_url:
        auto_answerer.start_monitoring(target_url)
        return jsonify({'status': 'started', 'message': 'تم بدء المراقبة'})
    else:
        return jsonify({'status': 'error', 'message': 'يرجى إدخال رابط صحيح'})

@control_app.route('/api/stop', methods=['POST'])
def stop_monitoring():
    """إيقاف المراقبة"""
    auto_answerer.stop_monitoring()
    return jsonify({'status': 'stopped', 'message': 'تم إيقاف المراقبة'})

@control_app.route('/api/answer', methods=['POST'])
def answer_question():
    """API للإجابة على الأسئلة من التطبيقات الخارجية"""
    try:
        data = request.get_json()
        question_text = data.get('question', '')
        
        if not question_text:
            return jsonify({'error': 'لم يتم تقديم سؤال'})
        
        if not auto_answerer.is_running:
            return jsonify({'error': 'النظام غير مفعل'})
        
        # معالجة السؤال
        result = auto_answerer.process_question(question_text)
        
        if result:
            return jsonify({
                'answer': result['answer'],
                'confidence': result['confidence'],
                'reasoning': result['reasoning'],
                'timestamp': result['timestamp'],
                'question_number': auto_answerer.stats['questions_answered']
            })
        else:
            return jsonify({'error': 'فشل في معالجة السؤال'})
            
    except Exception as e:
        return jsonify({'error': f'خطأ في الخادم: {str(e)}'})

@control_app.route('/api/stats')
def get_stats():
    """الحصول على الإحصائيات"""
    stats = auto_answerer.stats.copy()
    
    if stats['start_time']:
        elapsed = datetime.now() - stats['start_time']
        stats['elapsed_time'] = str(elapsed).split('.')[0]
        
        if stats['questions_answered'] > 0:
            stats['accuracy'] = (stats['correct_answers'] / stats['questions_answered']) * 100
        else:
            stats['accuracy'] = 0
    
    return jsonify(stats)

@control_app.route('/api/logs')
def get_logs():
    """الحصول على السجل"""
    return jsonify({'logs': auto_answerer.log_messages[-50:]})  # آخر 50 رسالة

@control_app.route('/api/test', methods=['POST'])
def test_question():
    """اختبار سؤال"""
    data = request.get_json()
    question_text = data.get('question', '')
    
    if not question_text:
        return jsonify({'error': 'لم يتم تقديم سؤال'})
    
    # معالجة السؤال بدون تسجيله في الإحصائيات
    temp_stats = auto_answerer.stats['questions_answered']
    result = auto_answerer.process_question(question_text)
    auto_answerer.stats['questions_answered'] = temp_stats  # إعادة العداد
    
    if result:
        return jsonify({
            'answer': result['answer'],
            'confidence': result['confidence'],
            'reasoning': result['reasoning'],
            'question_type': result['question']['type']
        })
    else:
        return jsonify({'error': 'فشل في معالجة السؤال'})

if __name__ == '__main__':
    print("🤖 نظام الإجابة التلقائية عبر الويب")
    print("=" * 50)
    print("🌐 واجهة التحكم: http://localhost:5001")
    print("📡 API للتطبيقات الخارجية: http://localhost:5001/api/answer")
    print("📊 الإحصائيات: http://localhost:5001/api/stats")
    print("📝 السجل: http://localhost:5001/api/logs")
    print("=" * 50)
    
    control_app.run(debug=True, host='0.0.0.0', port=5001)
