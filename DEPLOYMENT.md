# 🚀 دليل النشر - Deployment Guide

## النشر المحلي

### Docker
```bash
# بناء الصورة
docker build -t ai-question-answerer .

# تشغيل الحاوية
docker run -p 5000:5000 ai-question-answerer
```

### Docker Compose
```bash
# تشغيل مع docker-compose
docker-compose up -d

# إيقاف
docker-compose down
```

## النشر السحابي

### 1. Heroku (مجاني)

```bash
# تثبيت Heroku CLI
# https://devcenter.heroku.com/articles/heroku-cli

# تسجيل الدخول
heroku login

# إنشاء تطبيق
heroku create your-app-name

# إضافة متغيرات البيئة
heroku config:set HUGGINGFACE_API_TOKEN=your_token_here

# رفع الكود
git init
git add .
git commit -m "Initial deployment"
git push heroku main

# فتح التطبيق
heroku open
```

### 2. Railway (مجاني)

1. <PERSON><PERSON><PERSON><PERSON> إلى: https://railway.app
2. اربط حساب GitHub
3. انشئ مشروع جديد
4. اختر "Deploy from GitHub repo"
5. اختر مستودع المشروع
6. أضف متغير البيئة: `HUGGINGFACE_API_TOKEN`
7. سيتم النشر تلقائياً

### 3. Render (مجاني)

1. اذهب إلى: https://render.com
2. انشئ حساب جديد
3. اختر "New Web Service"
4. اربط مستودع GitHub
5. إعدادات النشر:
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn app:app`
   - **Environment**: Python 3
6. أضف متغيرات البيئة
7. انقر "Create Web Service"

### 4. Vercel (مجاني)

```bash
# تثبيت Vercel CLI
npm i -g vercel

# تسجيل الدخول
vercel login

# نشر التطبيق
vercel

# إضافة متغيرات البيئة
vercel env add HUGGINGFACE_API_TOKEN
```

### 5. PythonAnywhere (مجاني محدود)

1. انشئ حساب على: https://www.pythonanywhere.com
2. ارفع ملفات المشروع
3. انشئ Web App جديد
4. اختر Flask
5. اضبط ملف WSGI:
```python
import sys
sys.path.append('/home/<USER>/mysite')
from app import app as application
```

## إعدادات الإنتاج

### متغيرات البيئة المطلوبة:
```bash
FLASK_ENV=production
HUGGINGFACE_API_TOKEN=your_token_here
SECRET_KEY=your_secret_key_here
```

### إعدادات الأمان:
```python
# في config.py
class ProductionConfig(Config):
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get('SECRET_KEY')
    # إعدادات أمان إضافية
```

## مراقبة التطبيق

### فحص الصحة:
```bash
curl http://your-app-url/health
```

### السجلات:
```bash
# Heroku
heroku logs --tail

# Docker
docker logs container_name

# Railway
# من لوحة التحكم
```

## النسخ الاحتياطي

### قاعدة البيانات (إذا أضيفت لاحقاً):
```bash
# PostgreSQL
pg_dump database_url > backup.sql

# MongoDB
mongodump --uri connection_string
```

### الملفات:
```bash
# ضغط المشروع
tar -czf backup.tar.gz .

# رفع للتخزين السحابي
aws s3 cp backup.tar.gz s3://your-bucket/
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في المنفذ:**
```python
port = int(os.environ.get('PORT', 5000))
app.run(host='0.0.0.0', port=port)
```

2. **مشكلة في المتطلبات:**
```bash
pip freeze > requirements.txt
```

3. **خطأ في المسارات:**
```python
import os
basedir = os.path.abspath(os.path.dirname(__file__))
```

### مراقبة الأداء:
- استخدم New Relic أو Datadog
- فعل السجلات المفصلة
- راقب استخدام الذاكرة والمعالج

## التحديثات

### نشر تحديث جديد:
```bash
# Heroku
git push heroku main

# Railway
git push origin main

# Docker
docker build -t ai-question-answerer:v2 .
docker run -p 5000:5000 ai-question-answerer:v2
```

---

**ملاحظة:** تأكد من اختبار التطبيق محلياً قبل النشر!
