from flask import Flask, render_template, request, jsonify
import requests
import json
import time
import re
import os
from datetime import datetime
from dotenv import load_dotenv
from config import config

# تحميل متغيرات البيئة
load_dotenv()

app = Flask(__name__)

# تحميل الإعدادات
config_name = os.getenv('FLASK_ENV', 'development')
app.config.from_object(config.get(config_name, config['default']))

class AIQuestionAnswerer:
    def __init__(self, app_config=None):
        # استخدام Hugging Face API المجاني
        if app_config:
            model = app_config.get('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-medium')
            api_token = app_config.get('HUGGINGFACE_API_TOKEN', 'hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
            self.api_timeout = app_config.get('API_TIMEOUT', 5)
        else:
            model = os.getenv('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-medium')
            api_token = os.getenv('HUGGINGFACE_API_TOKEN', 'hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
            self.api_timeout = int(os.getenv('API_TIMEOUT', 5))

        self.api_url = f"https://api-inference.huggingface.co/models/{model}"
        self.headers = {
            "Authorization": f"Bearer {api_token}"
        }
        
    def analyze_question(self, question_text):
        """تحليل السؤال لتحديد نوعه والإجابة عليه"""
        question_text = question_text.strip()
        
        # تحديد نوع السؤال
        if self.is_true_false_question(question_text):
            return self.answer_true_false(question_text)
        elif self.is_multiple_choice_question(question_text):
            return self.answer_multiple_choice(question_text)
        else:
            return {"type": "unknown", "answer": "غير قادر على تحديد نوع السؤال"}
    
    def is_true_false_question(self, question):
        """تحديد إذا كان السؤال صح أم خطأ"""
        true_false_indicators = [
            "صح أم خطأ", "صحيح أم خاطئ", "True or False", 
            "صح/خطأ", "T/F", "صحيح/خاطئ"
        ]
        return any(indicator in question for indicator in true_false_indicators)
    
    def is_multiple_choice_question(self, question):
        """تحديد إذا كان السؤال اختيار متعدد"""
        choice_indicators = ["أ)", "ب)", "ج)", "د)", "A)", "B)", "C)", "D)"]
        return any(indicator in question for indicator in choice_indicators)
    
    def answer_true_false(self, question):
        """الإجابة على أسئلة صح/خطأ"""
        try:
            # استخراج النص الأساسي للسؤال
            main_question = re.split(r'صح أم خطأ|صحيح أم خاطئ|True or False', question)[0].strip()
            
            # استخدام الذكاء الاصطناعي للتحليل
            ai_response = self.get_ai_response(f"هل هذه العبارة صحيحة أم خاطئة؟ {main_question}")
            
            # تحليل الإجابة
            if any(word in ai_response.lower() for word in ['صحيح', 'صح', 'true', 'نعم']):
                answer = "صح"
                confidence = 0.8
            elif any(word in ai_response.lower() for word in ['خطأ', 'خاطئ', 'false', 'لا']):
                answer = "خطأ"
                confidence = 0.8
            else:
                # إجابة افتراضية بناءً على تحليل بسيط
                answer = self.simple_true_false_analysis(main_question)
                confidence = 0.6
            
            return {
                "type": "true_false",
                "answer": answer,
                "confidence": confidence,
                "reasoning": ai_response
            }
        except Exception as e:
            return {
                "type": "true_false",
                "answer": "صح",  # إجابة افتراضية
                "confidence": 0.5,
                "reasoning": f"خطأ في التحليل: {str(e)}"
            }
    
    def answer_multiple_choice(self, question):
        """الإجابة على أسئلة الاختيار المتعدد"""
        try:
            # استخراج الخيارات
            choices = self.extract_choices(question)
            main_question = self.extract_main_question(question)
            
            if not choices:
                return {"type": "multiple_choice", "answer": "أ", "confidence": 0.5}
            
            # استخدام الذكاء الاصطناعي للاختيار
            ai_prompt = f"السؤال: {main_question}\nالخيارات: {' '.join(choices)}\nما هو الجواب الصحيح؟"
            ai_response = self.get_ai_response(ai_prompt)
            
            # تحليل الإجابة
            best_choice = self.analyze_ai_choice(ai_response, choices)
            
            return {
                "type": "multiple_choice",
                "answer": best_choice,
                "confidence": 0.7,
                "reasoning": ai_response,
                "choices": choices
            }
        except Exception as e:
            return {
                "type": "multiple_choice",
                "answer": "أ",  # إجابة افتراضية
                "confidence": 0.5,
                "reasoning": f"خطأ في التحليل: {str(e)}"
            }
    
    def get_ai_response(self, prompt):
        """الحصول على إجابة من الذكاء الاصطناعي"""
        try:
            # استخدام نموذج مجاني من Hugging Face
            payload = {"inputs": prompt}
            response = requests.post(self.api_url, headers=self.headers, json=payload, timeout=self.api_timeout)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get('generated_text', prompt)
                return str(result)
            else:
                # في حالة فشل API، استخدم تحليل بسيط
                return self.simple_analysis(prompt)
        except:
            return self.simple_analysis(prompt)
    
    def simple_analysis(self, prompt):
        """تحليل بسيط في حالة عدم توفر AI"""
        if "صحيح" in prompt or "صح" in prompt:
            return "هذه العبارة تبدو صحيحة"
        elif "خطأ" in prompt or "خاطئ" in prompt:
            return "هذه العبارة تبدو خاطئة"
        else:
            return "تحليل بسيط للسؤال"
    
    def simple_true_false_analysis(self, question):
        """تحليل بسيط لأسئلة صح/خطأ"""
        # كلمات تشير إلى إجابة "صح"
        positive_words = ['دائماً', 'جميع', 'كل', 'أفضل', 'أكبر', 'أصغر']
        # كلمات تشير إلى إجابة "خطأ"  
        negative_words = ['أبداً', 'لا يمكن', 'مستحيل', 'خطأ']
        
        question_lower = question.lower()
        
        if any(word in question_lower for word in negative_words):
            return "خطأ"
        elif any(word in question_lower for word in positive_words):
            return "صح"
        else:
            return "صح"  # افتراضي
    
    def extract_choices(self, question):
        """استخراج الخيارات من السؤال"""
        choices = []
        patterns = [
            r'أ\)(.*?)(?=ب\)|$)',
            r'ب\)(.*?)(?=ج\)|$)',
            r'ج\)(.*?)(?=د\)|$)',
            r'د\)(.*?)(?=$)',
            r'A\)(.*?)(?=B\)|$)',
            r'B\)(.*?)(?=C\)|$)',
            r'C\)(.*?)(?=D\)|$)',
            r'D\)(.*?)(?=$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, question, re.IGNORECASE)
            if match:
                choices.append(match.group(1).strip())
        
        return choices
    
    def extract_main_question(self, question):
        """استخراج السؤال الأساسي"""
        # إزالة الخيارات من السؤال
        main_q = re.split(r'أ\)|ب\)|ج\)|د\)|A\)|B\)|C\)|D\)', question)[0]
        return main_q.strip()
    
    def analyze_ai_choice(self, ai_response, choices):
        """تحليل إجابة الذكاء الاصطناعي لاختيار الخيار الصحيح"""
        ai_response_lower = ai_response.lower()
        
        # البحث عن الحروف في الإجابة
        if 'أ' in ai_response or 'a' in ai_response_lower:
            return 'أ'
        elif 'ب' in ai_response or 'b' in ai_response_lower:
            return 'ب'
        elif 'ج' in ai_response or 'c' in ai_response_lower:
            return 'ج'
        elif 'د' in ai_response or 'd' in ai_response_lower:
            return 'د'
        
        # البحث عن محتوى الخيارات في الإجابة
        for i, choice in enumerate(choices):
            if choice.lower() in ai_response_lower:
                return ['أ', 'ب', 'ج', 'د'][i]
        
        return 'أ'  # افتراضي

# إنشاء مثيل من المجيب الذكي
ai_answerer = AIQuestionAnswerer(app.config)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/answer', methods=['POST'])
def answer_question():
    data = request.get_json()
    question = data.get('question', '')
    
    if not question:
        return jsonify({'error': 'لم يتم تقديم سؤال'})
    
    # تحليل السؤال والإجابة عليه
    result = ai_answerer.analyze_question(question)
    
    # إضافة الوقت
    result['timestamp'] = datetime.now().isoformat()
    result['processing_time'] = '< 1 second'
    
    return jsonify(result)

@app.route('/health')
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
