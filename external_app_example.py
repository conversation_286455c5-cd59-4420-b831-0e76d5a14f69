#!/usr/bin/env python3
"""
مثال على تطبيق خارجي يستخدم نظام الإجابة التلقائية
Example External App using Auto Answer System
"""

import requests
import time
import json
from datetime import datetime

class ExternalQuizApp:
    """مثال على تطبيق مسابقات خارجي"""
    
    def __init__(self, auto_answerer_url="http://localhost:5001"):
        self.auto_answerer_url = auto_answerer_url
        self.questions = [
            {
                "id": 1,
                "text": "الشمس تشرق من الشرق - صح أم خطأ؟",
                "type": "true_false",
                "correct_answer": "صح"
            },
            {
                "id": 2,
                "text": "ما هي عاصمة مصر؟\nأ) الإسكندرية\nب) القاهرة\nج) الجيزة\nد) أسوان",
                "type": "multiple_choice",
                "correct_answer": "ب"
            },
            {
                "id": 3,
                "text": "الأرض مسطحة - صحيح أم خاطئ؟",
                "type": "true_false",
                "correct_answer": "خطأ"
            },
            {
                "id": 4,
                "text": "ما هي أكبر قارة في العالم؟\nأ) أفريقيا\nب) آسيا\nج) أوروبا\nد) أمريكا الشمالية",
                "type": "multiple_choice",
                "correct_answer": "ب"
            },
            {
                "id": 5,
                "text": "الماء يغلي عند درجة 100 مئوية - صح أم خطأ؟",
                "type": "true_false",
                "correct_answer": "صح"
            }
        ]
        self.current_question_index = 0
        self.score = 0
        self.total_questions = len(self.questions)
    
    def send_question_to_ai(self, question_text):
        """إرسال السؤال إلى نظام الإجابة التلقائية"""
        try:
            response = requests.post(
                f"{self.auto_answerer_url}/api/answer",
                json={"question": question_text},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ خطأ في الخادم: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return None
    
    def check_auto_answerer_status(self):
        """فحص حالة نظام الإجابة التلقائية"""
        try:
            response = requests.get(f"{self.auto_answerer_url}/api/stats", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def display_question(self, question):
        """عرض السؤال"""
        print(f"\n📝 السؤال {question['id']} من {self.total_questions}:")
        print("=" * 50)
        print(f"❓ {question['text']}")
        print("=" * 50)
    
    def run_quiz(self):
        """تشغيل المسابقة"""
        print("🎯 مرحباً بك في مسابقة الذكاء!")
        print("🤖 سيتم استخدام نظام الإجابة التلقائية")
        print("=" * 60)
        
        # فحص اتصال نظام الإجابة التلقائية
        if not self.check_auto_answerer_status():
            print("❌ نظام الإجابة التلقائية غير متاح!")
            print("💡 تأكد من تشغيل: python web_auto_answerer.py")
            return
        
        print("✅ نظام الإجابة التلقائية متصل ويعمل")
        
        start_time = datetime.now()
        
        for question in self.questions:
            self.display_question(question)
            
            # إرسال السؤال إلى نظام الإجابة التلقائية
            print("🤖 جاري إرسال السؤال إلى الذكاء الاصطناعي...")
            
            ai_response = self.send_question_to_ai(question['text'])
            
            if ai_response and 'answer' in ai_response:
                ai_answer = ai_response['answer']
                confidence = ai_response.get('confidence', 0)
                
                print(f"🎯 إجابة الذكاء الاصطناعي: {ai_answer}")
                print(f"📊 مستوى الثقة: {confidence:.0%}")
                print(f"✅ الإجابة الصحيحة: {question['correct_answer']}")
                
                # فحص صحة الإجابة
                if ai_answer == question['correct_answer']:
                    print("🎉 إجابة صحيحة!")
                    self.score += 1
                else:
                    print("❌ إجابة خاطئة!")
                
                print(f"📈 النتيجة الحالية: {self.score}/{question['id']}")
                
            else:
                print("❌ فشل في الحصول على إجابة من الذكاء الاصطناعي")
                print(f"✅ الإجابة الصحيحة: {question['correct_answer']}")
            
            # انتظار قبل السؤال التالي
            if question['id'] < self.total_questions:
                print("\n⏳ انتظار 3 ثوانٍ قبل السؤال التالي...")
                time.sleep(3)
        
        # عرض النتيجة النهائية
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🏆 انتهت المسابقة!")
        print("=" * 60)
        print(f"📊 النتيجة النهائية: {self.score}/{self.total_questions}")
        print(f"📈 نسبة النجاح: {(self.score/self.total_questions)*100:.1f}%")
        print(f"⏱️ الوقت المستغرق: {str(duration).split('.')[0]}")
        
        if self.score == self.total_questions:
            print("🥇 ممتاز! إجابات صحيحة 100%")
        elif self.score >= self.total_questions * 0.8:
            print("🥈 جيد جداً! أداء ممتاز")
        elif self.score >= self.total_questions * 0.6:
            print("🥉 جيد! يمكن التحسين")
        else:
            print("📚 يحتاج إلى مزيد من التدريب")

class WebScrapingExample:
    """مثال على استخراج الأسئلة من موقع ويب"""
    
    def __init__(self, auto_answerer_url="http://localhost:5001"):
        self.auto_answerer_url = auto_answerer_url
    
    def simulate_web_scraping(self):
        """محاكاة استخراج الأسئلة من موقع ويب"""
        print("🌐 محاكاة استخراج الأسئلة من موقع ويب...")
        
        # محاكاة أسئلة مستخرجة من موقع
        web_questions = [
            "البرازيل هي أكبر دولة في أمريكا الجنوبية - صح أم خطأ؟",
            "ما هو أطول نهر في العالم؟\nأ) النيل\nب) الأمازون\nج) اليانغتسي\nد) المسيسيبي",
            "عدد قارات العالم هو سبع قارات - صحيح أم خاطئ؟"
        ]
        
        for i, question in enumerate(web_questions, 1):
            print(f"\n🔍 سؤال مستخرج {i}:")
            print(f"❓ {question}")
            
            # إرسال إلى نظام الإجابة التلقائية
            try:
                response = requests.post(
                    f"{self.auto_answerer_url}/api/answer",
                    json={"question": question},
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"🤖 الإجابة: {data.get('answer', 'غير محدد')}")
                    print(f"📊 الثقة: {data.get('confidence', 0):.0%}")
                else:
                    print("❌ خطأ في الحصول على الإجابة")
                    
            except Exception as e:
                print(f"❌ خطأ في الاتصال: {e}")
            
            time.sleep(2)

def main():
    """الدالة الرئيسية"""
    print("🚀 أمثلة على استخدام نظام الإجابة التلقائية")
    print("=" * 60)
    
    while True:
        print("\nاختر المثال:")
        print("1. مسابقة ذكية مع نظام الإجابة التلقائية")
        print("2. محاكاة استخراج أسئلة من موقع ويب")
        print("3. اختبار اتصال النظام")
        print("4. خروج")
        
        choice = input("\nأدخل اختيارك (1-4): ").strip()
        
        if choice == "1":
            quiz_app = ExternalQuizApp()
            quiz_app.run_quiz()
            
        elif choice == "2":
            scraper = WebScrapingExample()
            scraper.simulate_web_scraping()
            
        elif choice == "3":
            print("🔍 اختبار اتصال نظام الإجابة التلقائية...")
            try:
                response = requests.get("http://localhost:5001/api/stats", timeout=5)
                if response.status_code == 200:
                    print("✅ النظام متصل ويعمل بشكل صحيح")
                    data = response.json()
                    print(f"📊 الأسئلة المجابة: {data.get('questions_answered', 0)}")
                else:
                    print("❌ النظام لا يستجيب بشكل صحيح")
            except:
                print("❌ لا يمكن الاتصال بالنظام")
                print("💡 تأكد من تشغيل: python web_auto_answerer.py")
                
        elif choice == "4":
            print("👋 شكراً لاستخدام النظام!")
            break
            
        else:
            print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
