#!/usr/bin/env python3
"""
ملف اختبار التطبيق
"""

import requests
import json
import time

def test_local_server():
    """اختبار الخادم المحلي"""
    base_url = "http://localhost:5000"
    
    print("🧪 اختبار مجيب الأسئلة الذكي")
    print("=" * 40)
    
    # اختبار الصفحة الرئيسية
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
    except Exception as e:
        print(f"❌ لا يمكن الوصول للخادم: {e}")
        return
    
    # اختبار فحص الصحة
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ فحص الصحة يعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في فحص الصحة: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في فحص الصحة: {e}")
    
    # اختبار أسئلة صح/خطأ
    test_questions = [
        "الشمس تشرق من الشرق - صح أم خطأ؟",
        "الأرض مسطحة - صحيح أم خاطئ؟",
        "ما هي عاصمة مصر؟\nأ) الإسكندرية\nب) القاهرة\nج) الجيزة\nد) أسوان"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 اختبار السؤال {i}:")
        print(f"السؤال: {question[:50]}...")
        
        try:
            response = requests.post(
                f"{base_url}/answer",
                json={"question": question},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ الإجابة: {result.get('answer', 'غير محدد')}")
                print(f"📊 الثقة: {result.get('confidence', 0):.2f}")
                print(f"🔧 النوع: {result.get('type', 'غير محدد')}")
            else:
                print(f"❌ خطأ في الإجابة: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
        
        time.sleep(1)  # انتظار ثانية بين الاختبارات
    
    print("\n🎉 انتهى الاختبار!")

if __name__ == "__main__":
    test_local_server()
